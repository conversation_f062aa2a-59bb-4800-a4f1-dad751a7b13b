import { useAuthStore } from '@/store';
import _ from 'lodash';
import React, { useEffect, useState } from 'react'
import { useQueryAgxArea } from '../hooks/useQueryAgxArea';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useNavigate } from 'react-router-dom';
import { useCreateAgxSubArea } from '../hooks/useCreateAgxSubArea';
import { Input } from '@/components/ui/input';

const initialState = {
    agxAreas: [],
    agxSubAreas: [],
    agxSubAreaModal: [],
    loading: true,
    error: false
}

export const SubAreaNewPage = () => {

    const navigate = useNavigate();
    const { user } = useAuthStore();
    const agxMerchantNo = user.agxMerchantNo;
    const [dataAreaSetting, setDataAreaSetting] = useState(initialState);
    const [agxSubArea, setAgxSubArea] = useState({
        agx_sub_areaid: null,
        agxSubAreaName: '',
        agxMerchantCreatedNo: agxMerchantNo,
        agxMerchantNos: []
    })

    const { data: GetAreaSettingResponse, isLoading } = useQueryAgxArea({
        agxMerchantNo: user?.agxMerchantNo || "",
    });

    const { createAgxSubAreaAsync, isLoading: isLoadingCreateSubArea } = useCreateAgxSubArea();

    useEffect(() => {
        getDataAreaSetting();
    }, [GetAreaSettingResponse])

    const getDataAreaSetting = async () => {
        try {
            setDataAreaSetting({
                ...dataAreaSetting,
                agxAreas: GetAreaSettingResponse.agxAreas,
                agxSubAreas: GetAreaSettingResponse.agxSubAreas,
                agxSubAreaModal: GetAreaSettingResponse.agxSubAreaModal,
                loading: false,
                error: false
            });
        } catch (error) {
            setDataAreaSetting({
                ...dataAreaSetting,
                agxAreas: [],
                agxSubAreas: [],
                agxSubAreaModal: [],
                loading: true,
                error: true
            })
        }
    }

    const handleChangeSubArea = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.trim();
        setAgxSubArea({ ...agxSubArea, agxSubAreaName: value })
    }

    const handleCreateOrUpdateSubArea = async () => {
        try {
            await createAgxSubAreaAsync(agxSubArea);
            navigate('/admin-store/config');
        } catch (error) {
            console.log(error);
        }
    }

    const handleSelectMerchant = (
        event: React.ChangeEvent<HTMLInputElement>,
        merchantNo: string,
        subAreaId: string
    ) => {
        const isChecked = event.target.checked;
        const checkbox = document.getElementById(merchantNo) as HTMLInputElement | null;
        if (checkbox) {
            checkbox.checked = isChecked;
        }
        const arrSelected = agxSubArea.agxMerchantNos || [];

        let updatedMerchantNos: { agxMerchantNo: string; isSelected: boolean; isHadArea: boolean }[];

        if (arrSelected.length > 0) {
            const filteredMerchantSelected = arrSelected.filter(
                (item: { agxMerchantNo: string }) => item.agxMerchantNo !== merchantNo
            );
            filteredMerchantSelected.push({
                agxMerchantNo: merchantNo,
                isSelected: isChecked,
                isHadArea: subAreaId === agxSubArea.agx_sub_areaid ? true : false
            })
            updatedMerchantNos = filteredMerchantSelected;
        } else {
            updatedMerchantNos = [
                {
                    agxMerchantNo: merchantNo,
                    isSelected: true,
                    isHadArea: false
                }
            ];
        }
        setAgxSubArea({ ...agxSubArea, agxMerchantNos: updatedMerchantNos });
    }
    
    if (isLoading) {
        return <LoadingSpinner />;
    }

    return (
        <div className="pt-6 pb-2 px-2">
            <div className="max-w-full mx-auto px-4 sm:px-6 md:px-8 lg:mx-20 lg:mt-20 sm:mt-10 mt-4 lg:!mr-[25%]">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <h3 className="mb-2 md:mb-0 text-[#6F6F6E] text-lg sm:text-xl md:text-2xl font-medium">サブエリアの作成</h3>
                </div>
                <form>
                    {/* Sub Area Name Input - Responsive */}
                    <div className="mb-6 px-2 flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 ml-10">
                        <label htmlFor="sub-area-name" className="block text-[#6F6F6E] mb-2 sm:mb-0 sm:w-[25%] md:w-[25%] text-[20px]">
                            サブエリアの名称
                        </label>
                        {/* <input
                            type="text"
                            id="sub-area-name"
                            className="w-full sm:w-[75%] md:w-[50%] border rounded-[15px] border-gray-300 px-3 py-2 focus:outline-none text-[#6F6F6E] text-[20px]"
                            value={agxSubArea.agxSubAreaName}
                            onChange={handleChangeSubArea}
                        /> */}
                        <Input
                            type="text"
                            id="sub-area-name"
                            className="h-[45px] w-full min-w-[430px] rounded-[15px] border-gray-300 px-3 py-2 text-[#6F6F6E] text-[20px]"
                            value={agxSubArea.agxSubAreaName}
                            onChange={handleChangeSubArea}
                        />
                    </div>

                    {/* Merchant Selection */}
                    <div className="mb-2 ml-10">
                        <label className="block text-[#6F6F6E] mb-2 text-[20px] px-2">
                        サブエリアに紐づけする店舗の選択
                        </label>
                        
                        {/* Desktop/Tablet Table View */}
                        <div className="hidden lg:block bg-transparent rounded-md overflow-hidden">
                            {/* Fixed Header */}
                            <div className="bg-transparent px-2">
                                <div className="grid grid-cols-[48px_1fr_1fr_1fr] gap-0 text-[20px] border-b border-[#6F6F6E]">
                                    <div className="px-4 py-3 text-center font-medium text-[#6F6F6E]"></div>
                                    <div className="px-4 py-3 text-center font-medium text-[#6F6F6E]">加盟店番号</div>
                                    <div className="px-4 py-3 text-center font-medium text-[#6F6F6E]">店舗名</div>
                                    <div className="px-4 py-3 text-center font-medium text-[#6F6F6E]">サブエリア名</div>
                                </div>
                            </div>

                            {/* Scrollable Body */}
                            <div className="max-h-80 overflow-y-auto">
                                <div className="divide-y divide-gray-200">
                                    {dataAreaSetting?.agxSubAreaModal?.map((item, index) => (
                                        <div key={index} className="grid grid-cols-[48px_1fr_1fr_1fr] gap-0 hover:bg-gray-50 transition-colors border-none text-[20px]">
                                            <div className="pr-3 py-4 text-center flex items-center justify-center">
                                                <input
                                                    id={item.agxMerchantNo}
                                                    type="checkbox"
                                                    className="form-checkbox h-5 w-5 rounded border-gray-300 text-[#1D9987] accent-[#1D9987] cursor-pointer"
                                                    checked={!!agxSubArea.agxMerchantNos?.find((m) => m.agxMerchantNo === item.agxMerchantNo && m.isSelected)}
                                                    onChange={(e) => handleSelectMerchant(e, item.agxMerchantNo, item.agxSubAreaid)}
                                                />
                                            </div>
                                            <div className="px-4 py-4 text-center flex items-center justify-center text-[#6F6F6E]">{item.agxMerchantNo}</div>
                                            <div className="px-4 py-4 text-center flex items-center justify-center text-[#6F6F6E]">{item.agxStoreName}</div>
                                            <div className="px-4 py-4 text-center flex items-center justify-center text-[#6F6F6E]">{item.agxSubAreaName}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Tablet Simplified Table View */}
                        <div className="hidden md:block lg:hidden bg-transparent rounded-md overflow-hidden">
                            {/* Fixed Header */}
                            <div className="bg-transparent border-b border-[#6F6F6E]">
                                <div className="grid grid-cols-[48px_1fr_1fr] gap-0">
                                    <div className="px-3 py-3 text-center font-medium text-[#6F6F6E] text-sm"></div>
                                    <div className="px-3 py-3 text-center font-medium text-[#6F6F6E] text-sm">加盟店番号</div>
                                    <div className="px-3 py-3 text-center font-medium text-[#6F6F6E] text-sm">店舗名</div>
                                </div>
                            </div>

                            {/* Scrollable Body */}
                            <div className="max-h-80 overflow-y-auto">
                                <div className="divide-y divide-gray-200">
                                    {dataAreaSetting?.agxSubAreaModal?.map((item, index) => (
                                        <div key={index} className="grid grid-cols-[48px_1fr_1fr] gap-0 hover:bg-gray-50 transition-colors border-none">
                                            <div className="px-3 py-3 text-center flex items-center justify-center">
                                                <input
                                                    id={`tablet-${item.agxMerchantNo}`}
                                                    type="checkbox"
                                                    className="form-checkbox h-5 w-5 text-white rounded border-gray-300 text-[#1D9987] accent-[#1D9987]"
                                                    checked={!!agxSubArea.agxMerchantNos?.find((m) => m.agxMerchantNo === item.agxMerchantNo && m.isSelected)}
                                                    onChange={(e) => handleSelectMerchant(e, item.agxMerchantNo, item.agxSubAreaid)}
                                                />
                                            </div>
                                            <div className="px-3 py-3 text-center flex items-center justify-center text-[#6F6F6E] text-sm">{item.agxMerchantNo}</div>
                                            <div className="px-3 py-3 text-center flex items-center justify-center text-[#6F6F6E] text-sm truncate">{item.agxStoreName}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Mobile Card View */}
                        <div className="md:hidden">
                            <div className="max-h-80 overflow-y-auto space-y-3">
                                {dataAreaSetting?.agxSubAreaModal?.map((item, index) => (
                                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                        <div className="flex items-start space-x-3">
                                            <input
                                                id={`mobile-${item.agxMerchantNo}`}
                                                type="checkbox"
                                                className="form-checkbox h-5 w-5 text-white rounded border-gray-300 text-[#1D9987] accent-[#1D9987] mt-1 flex-shrink-0"
                                                checked={!!agxSubArea.agxMerchantNos?.find((m) => m.agxMerchantNo === item.agxMerchantNo && m.isSelected)}
                                                onChange={(e) => handleSelectMerchant(e, item.agxMerchantNo, item.agxSubAreaid)}
                                            />
                                            <div className="flex-1 space-y-2">
                                                <div>
                                                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">加盟店番号</span>
                                                    <p className="text-[#6F6F6E] text-sm font-medium">{item.agxMerchantNo}</p>
                                                </div>
                                                <div>
                                                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">店舗名</span>
                                                    <p className="text-[#6F6F6E] text-sm">{item.agxStoreName}</p>
                                                </div>
                                                <div>
                                                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">サブエリア名</span>
                                                    <p className="text-[#6F6F6E] text-sm">{item.agxSubAreaName}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </form>

                {/* Submit Button - Responsive */}
                <div className="flex flex-col mb-6 pb-4 items-center sm:items-end justify-center sm:justify-end">
                    <Button
                        disabled={isLoadingCreateSubArea}
                        className="w-full sm:w-[60%] md:w-[40%] lg:w-[22%] bg-transparent hover:bg-transparent text-[#707070] font-bold border border-[#707070] px-4 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-md text-lg sm:text-xl"
                        onClick={handleCreateOrUpdateSubArea}
                    >
                        登錄
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default SubAreaNewPage;
