import { useAuthStore } from '@/store';
import _ from 'lodash';
import { useEffect, useState } from 'react'
import { useQueryAgxArea } from '../hooks/useQueryAgxArea';
import { useDeleteAgxArea } from '../hooks/useDeleteAgxArea';
import { useDeleteAgxSubArea } from '../hooks/useDeleteAgxSubArea';
import { Button } from '@/components/ui/button';
import { ConfirmDeleteModal } from './ConfirmDeleteModal';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useNavigate } from 'react-router-dom';

const initialState = {
  agxAreas: [],
  agxSubAreas: [],
  agxSubAreaModal: [],
  loading: true,
  error: false
}

export const AreaSettingPage = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const agxMerchantNo = user.agxMerchantNo;
  const [dataAreaSetting, setDataAreaSetting] = useState(initialState);
  const [agxArea, setAgxArea] = useState({
    agx_areaid: null,
    agxMerchantCreatedNo: agxMerchantNo,
    agxAreaName: '',
    agxSubAreaids: []
  })
  const [agxSubArea, setAgxSubArea] = useState({
    agx_sub_areaid: null,
    agxSubAreaName: '',
    agxMerchantCreatedNo: agxMerchantNo,
    agxMerchantNos: []
  })

  const [deleteDialog, setDeleteDialog] = useState<{ type: 'area' | 'subarea' | null, id: string | null }>({ type: null, id: null });
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { data: GetAreaSettingResponse, isLoading, refetch } = useQueryAgxArea({
    agxMerchantNo: user?.agxMerchantNo || "",
  });

  const { deleteAgxAreaAsync } = useDeleteAgxArea();
  const { deleteAgxSubAreaAsync } = useDeleteAgxSubArea();

  useEffect(() => {
    getDataAreaSetting();
  }, [GetAreaSettingResponse])

  useEffect(() => {
    refetch();
  }, [])

  const getDataAreaSetting = async () => {
    try {
      setDataAreaSetting({
        ...dataAreaSetting,
        agxAreas: GetAreaSettingResponse.agxAreas,
        agxSubAreas: GetAreaSettingResponse.agxSubAreas,
        agxSubAreaModal: GetAreaSettingResponse.agxSubAreaModal,
        loading: false,
        error: false
      });
    } catch (error) {
      setDataAreaSetting({
        ...dataAreaSetting,
        agxAreas: [],
        agxSubAreas: [],
        agxSubAreaModal: [],
        loading: true,
        error: true
      })
    }
  }

  const handleDeleteArea = (agxAreaId: string) => {
    setDeleteDialog({ type: 'area', id: agxAreaId });
    setIsDialogOpen(true);
  };

  const handleDeleteSubArea = (agxSubAreaId: string) => {
    setDeleteDialog({ type: 'subarea', id: agxSubAreaId });
    setIsDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deleteDialog.type === 'area' && deleteDialog.id) {
      await deleteAgxAreaAsync(deleteDialog.id);
      await refetch();
    } else if (deleteDialog.type === 'subarea' && deleteDialog.id) {
      await deleteAgxSubAreaAsync(deleteDialog.id);
      await refetch();
    }
    setIsDialogOpen(false);
    setDeleteDialog({ type: null, id: null });
  };

  const handleClearCheckBox = () => {
    const cList = document.getElementsByTagName("input");
    for (let i = 0; i < cList.length; ++i) { cList[i].checked = false; }
  }

  const handleClearDataArea = () => {
    const cList = document.getElementsByTagName("input");
    for (let i = 0; i < cList.length; ++i) { cList[i].checked = false; }
    // Reset state to clear all selections
    setAgxArea({
      agx_areaid: null,
      agxMerchantCreatedNo: agxMerchantNo,
      agxAreaName: '',
      agxSubAreaids: []
    })
  }

  const handleSlectAreaDetail = (agxAreaid: string, agxAreaName: string) => {
    handleClearCheckBox();
    const arrSubSelect = _.filter(dataAreaSetting?.agxSubAreas, ['agxAreaid', agxAreaid]);
    const arrSelected: { agxSubAreaid: string; isSelected: boolean; isHadArea: boolean }[] = [];
    arrSubSelect.forEach(subArea => {
      const checkbox = document.getElementById(subArea.agxSubAreaid) as HTMLInputElement | null;
      if (checkbox) {
        checkbox.checked = true;
      }
      arrSelected.push({
        agxSubAreaid: subArea.agxSubAreaid,
        isSelected: true,
        isHadArea: true,
      })
    });
    setAgxArea({
      ...agxArea,
      agx_areaid: agxAreaid,
      agxAreaName: agxAreaName,
      agxSubAreaids: arrSelected
    });
  }

  const handleSelectSubAreaDetail = (subAreaid: string, subAreaName: string) => {
    // Clear any existing selections first
    handleClearCheckBox();

    // Find merchants that belong to this sub area
    const arrMerchantSelect = _.filter(dataAreaSetting?.agxSubAreaModal, ['agxSubAreaid', subAreaid]);
    const arrSelected: { agxMerchantNo: string; isSelected: boolean; isHadArea: boolean }[] = [];

    // Build the selected merchants array - React will handle the checkbox state
    arrMerchantSelect.forEach(merchant => {
      const checkbox = document.getElementById(merchant.agxMerchantNo) as HTMLInputElement | null;
      if (checkbox) {
        checkbox.checked = true;
      }
      arrSelected.push({
        agxMerchantNo: merchant.agxMerchantNo,
        isSelected: true,
        isHadArea: true,
      })
    });

    // Set the sub area state - this will automatically check the checkboxes via React
    setAgxSubArea({
      ...agxSubArea,
      agx_sub_areaid: subAreaid,
      agxSubAreaName: subAreaName,
      agxMerchantNos: arrSelected
    })
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="pt-6 pb-2 px-2">
      <div className="max-w-full mx-auto px-4 sm:px-6 md:px-8 lg:mx-20 lg:mt-20 sm:mt-10 mt-4 lg:!mr-[13%]">
        
        {/* Area Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 border-b border-[#707070] pb-4 space-y-3 sm:space-y-0">
          <h3 className="font-semibold text-[#6F6F6E] text-lg sm:text-xl md:text-2xl px-3">エリア</h3>
          <Button
            className="w-full sm:w-auto sm:min-w-[180px] bg-transparent hover:bg-transparent text-[#707070] font-bold border border-[#707070] px-4 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-md text-base sm:text-lg"
            onClick={() => navigate('/admin-store/config/area')}
          >
            エリアの作成
          </Button>
        </div>

        {/* Areas Table - Desktop/Tablet */}
        <div className="sm:block overflow-y-auto mb-8 max-h-[40vh]">
          <table className="min-w-full bg-white rounded shadow overflow-hidden">
            <thead>
              <tr>
                <th className="px-4 py-3 text-left bg-gray-100 text-[#6F6F6E] text-base md:text-lg">エリア名</th>
                <th className="px-4 py-3 bg-gray-100 w-32"></th>
              </tr>
            </thead>
            <tbody>
              {dataAreaSetting?.agxAreas?.map((item, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-3">
                    <div
                      className="text-[#6F6F6E] text-sm md:text-base hover:underline cursor-pointer"
                      onClick={() => handleSlectAreaDetail(item.agx_areaid, item.agxAreaName)}
                    >
                      {item.agxAreaName}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-end">
                    <Button
                      className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-2 px-4 md:py-3 md:px-8 rounded transition text-sm md:text-base"
                      onClick={() => handleDeleteArea(item.agx_areaid)}
                    >
                      削除
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Areas Mobile Cards */}
        {/* <div className="sm:hidden mb-8 space-y-3">
          {dataAreaSetting?.agxAreas?.map((item, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">エリア名</span>
                  <p 
                    className="text-[#6F6F6E] text-base font-medium hover:underline cursor-pointer mt-1"
                    onClick={() => handleSlectAreaDetail(item.agx_areaid, item.agxAreaName)}
                  >
                    {item.agxAreaName}
                  </p>
                </div>
                <Button
                  className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-2 px-4 rounded transition text-sm ml-3"
                  onClick={() => handleDeleteArea(item.agx_areaid)}
                >
                  削除
                </Button>
              </div>
            </div>
          ))}
        </div> */}

        {/* SubArea Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 border-b border-[#707070] pb-4 space-y-3 sm:space-y-0">
          <h3 className="font-semibold text-[#6F6F6E] text-lg sm:text-xl md:text-2xl px-3">サブエリア</h3>
          <Button
            className="w-full sm:w-auto sm:min-w-[180px] bg-transparent hover:bg-transparent text-[#707070] font-bold border border-[#707070] px-4 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-md text-base sm:text-lg"
            onClick={() => navigate('/admin-store/config/sub-area')}
          >
            サブエリアの作成
          </Button>
        </div>

        {/* SubAreas Table - Desktop */}
        <div className="hidden md:block overflow-x-auto mb-8 max-h-[40vh]">
          <table className="min-w-full bg-white rounded shadow overflow-hidden text-left">
            <thead>
              <tr>
                <th className="px-4 py-3 bg-gray-100 text-[#6F6F6E] text-base md:text-lg w-1/3">サブエリア名</th>
                <th className="px-4 py-3 bg-gray-100 text-[#6F6F6E] text-base md:text-lg w-1/3">エリア名</th>
                <th className="px-4 py-3 bg-gray-100 text-[#6F6F6E] text-base md:text-lg w-1/3"></th>
              </tr>
            </thead>
            <tbody>
              {dataAreaSetting?.agxSubAreas?.map((item, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-3">
                    <div
                      className="text-[#6F6F6E] text-sm md:text-base hover:underline cursor-pointer"
                      onClick={() => handleSelectSubAreaDetail(item.agxSubAreaid, item.agxSubAreaName)}
                    >
                      {item.agxSubAreaName}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-[#6F6F6E] text-sm md:text-base">{item.agxAreaName}</td>
                  <td className="px-4 py-3 text-end">
                    <Button
                      className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-2 px-4 md:py-3 md:px-8 rounded transition text-sm md:text-base"
                      onClick={() => handleDeleteSubArea(item.agxSubAreaid)}
                    >
                      削除
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* SubAreas Table - Tablet (2 columns) */}
        <div className="sm:block md:hidden overflow-x-auto mb-8 max-h-[40vh]">
          <table className="min-w-full bg-white rounded shadow overflow-hidden text-left">
            <thead>
              <tr>
                <th className="px-3 py-3 bg-gray-100 text-[#6F6F6E] text-sm">サブエリア名</th>
                <th className="px-3 py-3 bg-gray-100 text-[#6F6F6E] text-sm w-24"></th>
              </tr>
            </thead>
            <tbody>
              {dataAreaSetting?.agxSubAreas?.map((item, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="px-3 py-3">
                    <div
                      className="text-[#6F6F6E] text-sm hover:underline cursor-pointer font-medium"
                      onClick={() => handleSelectSubAreaDetail(item.agxSubAreaid, item.agxSubAreaName)}
                    >
                      {item.agxSubAreaName}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">エリア: {item.agxAreaName}</div>
                  </td>
                  <td className="px-3 py-3 text-end">
                    <Button
                      className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-2 px-3 rounded transition text-xs"
                      onClick={() => handleDeleteSubArea(item.agxSubAreaid)}
                    >
                      削除
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* SubAreas Mobile Cards */}
        {/* <div className="sm:hidden mb-8 space-y-3">
          {dataAreaSetting?.agxSubAreas?.map((item, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-2">
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">サブエリア名</span>
                    <p 
                      className="text-[#6F6F6E] text-base font-medium hover:underline cursor-pointer mt-1"
                      onClick={() => handleSelectSubAreaDetail(item.agxSubAreaid, item.agxSubAreaName)}
                    >
                      {item.agxSubAreaName}
                    </p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">エリア名</span>
                    <p className="text-[#6F6F6E] text-sm mt-1">{item.agxAreaName}</p>
                  </div>
                </div>
                <Button
                  className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-2 px-4 rounded transition text-sm ml-3"
                  onClick={() => handleDeleteSubArea(item.agxSubAreaid)}
                >
                  削除
                </Button>
              </div>
            </div>
          ))}
        </div> */}

        <ConfirmDeleteModal
          isDialogOpen={isDialogOpen}
          setIsDialogOpen={setIsDialogOpen}
          handleConfirmDelete={handleConfirmDelete}
        />
      </div>
    </div>
  )
}