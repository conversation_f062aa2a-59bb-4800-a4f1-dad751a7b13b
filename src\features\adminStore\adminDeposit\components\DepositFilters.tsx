import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

type AreaFilters = {
  areaSelected: string;
  subAreaSelected: string;
  merchantSelected: string;
};

interface DepositFiltersProps {
  filters: AreaFilters;
  onFiltersChange: (filters: Partial<AreaFilters>) => void;
  onSearch: () => void;
  filteredAreas: any[];
  filteredSubAreas: any[];
  filteredMerchants: any[];
}

export const DepositFilters: React.FC<DepositFiltersProps> = ({
  filters,
  onFiltersChange,
  onSearch,
  filteredAreas,
  filteredSubAreas,
  filteredMerchants
}) => {
  return (
    <div className="md:px-2 lg:px-6 xl:py-4 text-[#6F6F6E] text-[20px] flex flex-col xl:flex-row xl:gap-16 items-center">
      <div className="flex flex-wrap gap-8 xl:gap-[5.5rem] py-6 justify-between items-center">
        <div className="flex items-center gap-14 lg:gap-9">
          <Label htmlFor="area" className='text-2xl'>
            エリア
          </Label>
          <Select
            value={filters.areaSelected}
            onValueChange={(value) => onFiltersChange({ areaSelected: value })}
          >
            <div className="relative min-w-[200px] md:min-w-[260px]">
              <SelectTrigger id="area" className="text-[#6F6F6E] text-xl border-gray-300 border-2 shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                <SelectValue placeholder="全て" />
              </SelectTrigger>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
              </div>
            </div>
            <SelectContent>
              <SelectItem value="all" className="text-[#6F6F6E] text-xl">全て</SelectItem>
              {filteredAreas?.map((item, index) => (
                <SelectItem key={index} value={item.agx_areaid} className="text-[#6F6F6E] text-xl">
                  {item.agxAreaName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2 lg:gap-9">
          <Label htmlFor="sub-area" className='text-2xl'>
            サブエリア
          </Label>
          <Select
            value={filters.subAreaSelected}
            onValueChange={(value) => onFiltersChange({ subAreaSelected: value })}
          >
            <div className="relative min-w-[200px] md:min-w-[260px]">
              <SelectTrigger id="sub-area" className="text-[#6F6F6E] text-xl border-gray-300 border-2 shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                <SelectValue placeholder="全て" />
              </SelectTrigger>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
              </div>
            </div>
            <SelectContent>
              <SelectItem value="all" className="text-[#6F6F6E] text-xl">全て</SelectItem>
              {filteredSubAreas?.map((item, index) => (
                <SelectItem key={index} value={item.agxSubAreaid} className="text-[#6F6F6E] text-xl">
                  {item.agxSubAreaName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-14 lg:gap-9">
          <Label htmlFor="merchant" className='text-2xl'>
            加盟店
          </Label>
          <Select
            value={filters.merchantSelected}
            onValueChange={(value) => onFiltersChange({ merchantSelected: value })}
          >
            <div className="relative min-w-[200px] md:min-w-[260px]">
              <SelectTrigger id="merchant" className="text-[#6F6F6E] text-xl border-gray-300 border-2 shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                <SelectValue placeholder="全て" />
              </SelectTrigger>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
              </div>
            </div>
            <SelectContent>
              <SelectItem value="all" className="text-[#6F6F6E] text-xl">全て</SelectItem>
              {filteredMerchants?.map((item, index) => (
                <SelectItem key={index} value={item.agxMerchantNo} className="text-[#6F6F6E] text-xl">
                  {item.agxStoreName}-{item.agxMerchantNo}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="flex items-center justify-center my-4 xl:my-0">
        <button
          onClick={onSearch}
          className="w-[150px] bg-[#1D9987] hover:bg-[#1D9987]/80 text-white px-8 py-1 text-xl rounded-xl"
        >
          検索
        </button>
      </div>
    </div>
  );
};
