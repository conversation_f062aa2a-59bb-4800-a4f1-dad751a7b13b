import React from 'react';
import { Link } from 'react-router-dom';
import { formatNumber } from '@/utils/dateUtils';
import { STORE } from '@/types/globalType';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useDynamicTableWidth } from '@/hooks/useDynamicTableWidth';

interface MerchantPayment {
  agxNumberOfSales?: number;
  agxSalesAmount?: number;
  agxTotalFee?: number;
  agxSettlementCompanyTax?: number;
  agxInHouseTax?: number;
  agxPaymentAmount?: number;
  agxInvoiceFlg?: number;
  agxBankName?: string;
  agxBranchName?: string;
  agxAcccountType?: string;
  agxAccountNo?: string;
  agxAccountHolder?: string;
  // Admin view fields
  numberOfSales?: number;
  salesAmount?: number;
  totalFee?: number;
  sumTax?: number;
  paymentAmount?: number;
}

interface StoreSummaryTableProps {
  merchantPayments?: MerchantPayment[];
  total?: number;
  transferDate: string;
  type?: string;
  isNewFormat?: boolean;
  showMonthlyFee?: boolean;
  isAdminView?: boolean;
  detailLink?: string;
}

export const StoreSummaryTable: React.FC<StoreSummaryTableProps> = ({
  merchantPayments = [],
  total,
  transferDate,
  type = '',
  isNewFormat = false,
  showMonthlyFee = false,
  isAdminView = false,
  detailLink
}) => {
  const tableMaxWidth = useDynamicTableWidth();
  // For admin view, use the last payment (total), for store view use the first
  const merchantPayment = isAdminView
    ? merchantPayments[merchantPayments.length - 1]
    : merchantPayments[0];

  if (!merchantPayment) return null;

  // Get values based on view type
  const numberOfSales = isAdminView
    ? merchantPayment.numberOfSales
    : merchantPayment.agxNumberOfSales;
  const salesAmount = isAdminView
    ? merchantPayment.salesAmount
    : merchantPayment.agxSalesAmount;
  const totalFeeAmount = isAdminView
    ? merchantPayment.totalFee
    : merchantPayment.agxTotalFee;
  const taxAmount = isAdminView
    ? merchantPayment.sumTax
    : (merchantPayment.agxSettlementCompanyTax || 0) + (merchantPayment.agxInHouseTax || 0);
  const paymentAmount = isAdminView
    ? merchantPayment.paymentAmount
    : merchantPayment.agxPaymentAmount;

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-3 gap-6 text-[#6F6F6E] mt-4 ${isAdminView ? '' : 'sm:min-w-[1200px]'}`}>
      <div className={`${isAdminView ? 'sm:col-span-3 xl:col-span-2' : 'sm:col-span-2'} border-b border-[#6F6F6E] pb-4`}>
        <div className="">
          <div className="flex item-center text-2xl text-[#6F6F6E] md:px-2 lg:px-4">
            <span className="mr-2">サマリー</span>
            <span className="w-px h-6 bg-[#6F6F6E] inline-block mx-2 text-[#6F6F6E] my-auto" />
            <Link
              to={isAdminView ? detailLink : `/store/deposit/${type}/detail/${transferDate}`}
              className="text-[#1D9987] hover:text-[#1D9987]/80 text-xl font-medium ml-2"
            >
              詳細データを確認する
            </Link>
          </div>
        </div>

        <div className="md:px-4 xl:pl-8">
          <div className="bg-white w-full overflow-x-auto" style={{ maxWidth: tableMaxWidth }}>
            <Table className="text-xl min-w-max text-[#6F6F6E]">
              <TableHeader>
                <TableRow className="border-none hover:bg-white">
                  <TableHead className="pt-3 text-center bg-white">
                    <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-2 text-[#6F6F6E]">売上件数</span>
                  </TableHead>
                  <TableHead className="pt-3 text-center bg-white">
                    <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-2 text-[#6F6F6E]">売上金額</span>
                  </TableHead>
                  <TableHead className="pt-3 text-center bg-white">
                    <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-2 text-[#6F6F6E]">手数料額</span>
                  </TableHead>
                  <TableHead className="pt-3 text-center bg-white">
                    <span className="w-[80%] inline-block border-b border-[#6F6F6E] px-2 py-2 text-[#6F6F6E]">（内消費税）</span>
                  </TableHead>
                  {showMonthlyFee && (
                    <TableHead className="pt-3 text-center bg-white">
                      <span className="w-full inline-block border-b border-[#6F6F6E] px-2 py-2 text-[#6F6F6E]">月額費用</span>
                    </TableHead>
                  )}
                  <TableHead className="pt-3 text-center bg-white">
                    <span className='w-full inline-block border-b border-[#6F6F6E] px-2 py-2 text-[#6F6F6E]'>{isAdminView ? '振込金額' : (type === 'paygate' ? '振込金額' : '振込額')}</span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow className="border-b-0 hover:bg-white">
                  <TableCell className="p-0 pt-2 text-center">
                    {numberOfSales !== null && numberOfSales !== undefined ? `${formatNumber(numberOfSales)}件` : ''}
                  </TableCell>
                  <TableCell className="p-0 pt-2 text-center">
                    {salesAmount !== null && salesAmount !== undefined ? `${formatNumber(salesAmount)}円` : ''}
                  </TableCell>
                  <TableCell className="p-0 pt-2 text-center">
                    {totalFeeAmount !== null && totalFeeAmount !== undefined ? `${formatNumber(totalFeeAmount)}円` : ''}
                  </TableCell>
                  <TableCell className="p-0 pt-2 text-center">
                    {`(${formatNumber(taxAmount || 0)})円`}
                  </TableCell>
                  {showMonthlyFee && (
                    <TableCell className="p-0 pt-2 text-center">
                      {total !== null && total !== undefined ? `${formatNumber(total)}円` : ''}
                    </TableCell>
                  )}
                  <TableCell className="p-0 pt-2 text-center">
                    {paymentAmount !== null && paymentAmount !== undefined ? `${formatNumber(paymentAmount)}円` : ''}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>

        {!isAdminView && (
          <div className="mt-8 text-[#6F6F6E] text-lg md:px-8 xl:pl-16">
            <div>※毎月初の振込は、売上金額より月額費用を差し引いております。売上金額が月額費用未満であった場合、</div>
            <div>&nbsp;&nbsp;&nbsp;&nbsp;別途月額費用を弊社宛てにお支払いいただく必要があります。</div>
          </div>
        )}
      </div>

      {/* Bank Info - Only for store view */}
      {!isAdminView && (
        <div className='md:px-8 pb-4'>
          {isNewFormat && (
            <div className="mb-4">
              <div className="text-[#6F6F6E] text-lg">適格事業者登録番号</div>
              <div className="text-[#6F6F6E] text-lg">(T6120001228218)</div>
            </div>
          )}

          <div className="mb-2 text-[#6F6F6E] text-lg">振込先金融機関</div>
          <div className="text-lg space-y-1">
            <div className="flex">
              <span className="w-24">金融機関名</span>
              <span className="mr-2">:</span>
              <span>{merchantPayment.agxBankName || ''}</span>
            </div>
            <div className="flex">
              <span className="w-24">支店名</span>
              <span className="mr-2">:</span>
              <span>{merchantPayment.agxBranchName || ''}</span>
            </div>
            <div className="flex">
              <span className="w-24">口座種別</span>
              <span className="mr-2">:</span>
              <span>{merchantPayment.agxAcccountType || ''}</span>
            </div>
            <div className="flex">
              <span className="w-24">口座番号</span>
              <span className="mr-2">:</span>
              <span>{merchantPayment.agxAccountNo || ''}</span>
            </div>
            <div className="flex">
              <span className="w-24">名義人</span>
              <span className="mr-2">:</span>
              <span>{merchantPayment.agxAccountHolder || ''}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
